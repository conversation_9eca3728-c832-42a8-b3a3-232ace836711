<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="androidfwk-page-title">AndroidFwk集成能力 - 3D引擎移动端开发</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/prism.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
    <style>
        /* 响应式时间线样式 */
        @media (max-width: 768px) {
            .timeline-item {
                flex-direction: column !important;
                margin-bottom: 40px !important;
            }
            .timeline-axis {
                display: none !important;
            }
            .timeline-node {
                position: relative !important;
                left: auto !important;
                top: auto !important;
                transform: none !important;
                margin: 0 auto 20px auto !important;
            }
            .case-content-left, .case-content-right, .case-image-left, .case-image-right {
                width: 100% !important;
                padding: 0 !important;
                margin: 0 !important;
                text-align: left !important;
            }
            .case-content-left {
                order: 2;
            }
            .case-image-left, .case-image-right {
                order: 1;
                margin-bottom: 20px !important;
            }
        }

        /* 时间线动画效果 */
        .timeline-item {
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 0.8s ease forwards;
        }

        .timeline-item:nth-child(2) { animation-delay: 0.2s; }
        .timeline-item:nth-child(3) { animation-delay: 0.4s; }
        .timeline-item:nth-child(4) { animation-delay: 0.6s; }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 卡片悬停效果 */
        .case-card {
            transition: all 0.3s ease;
        }

        .case-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 16px 48px rgba(0,0,0,0.15) !important;
        }

        /* 图片容器悬停效果 */
        .image-container {
            transition: all 0.3s ease;
        }

        .image-container:hover {
            transform: scale(1.02);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <span class="logo-text" data-i18n="nav-logo-text">3D 引擎技术对比</span>
            </div>
            <div class="nav-menu">
                <a href="../index.html#engines" class="nav-link" data-i18n="nav-engines">引擎对比</a>
                <a href="../index.html#features" class="nav-link" data-i18n="nav-features">特性分析</a>
                <a href="../index.html#i18n" class="nav-link" data-i18n="nav-guides">国际化</a>
                <a href="../index.html#docs" class="nav-link" data-i18n="nav-resources">文档</a>
                <div class="language-selector">
                    <select id="languageSelect">
                        <option value="zh" data-i18n="lang-zh">中文</option>
                        <option value="en" data-i18n="lang-en">English</option>
                    </select>
                </div>
            </div>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <section class="hero" style="padding: 88px 0 48px; min-height: 40vh;">
        <div class="hero-container">
            <h1 class="hero-title" data-i18n="androidfwk-hero-title">Android Framework 集成能力</h1>
            <div class="framework-explanation" style="margin: 16px auto 24px; max-width: 600px;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 16px 24px; border-radius: 12px; text-align: center;">
                    <span style="font-size: 14px; opacity: 0.9;" data-i18n="framework-full-name">Android Framework (AndroidFwk)</span>
                    <div style="font-size: 12px; margin-top: 4px; opacity: 0.8;" data-i18n="framework-explanation">Android系统应用程序框架层的核心组件</div>
                </div>
            </div>
            <p class="hero-subtitle" data-i18n="androidfwk-hero-subtitle">移动端3D应用开发的专业集成方案</p>
            <p class="hero-description" data-i18n="androidfwk-hero-description" style="max-width: 700px; margin: 0 auto 32px;">
                Android Framework模块为移动端3D应用开发提供了高效的集成方案，通过深度集成Android系统框架层，
                支持多种3D渲染功能与原生系统能力，助力开发者快速构建高性能的跨平台3D移动应用。
            </p>
        </div>
    </section>

    <!-- 主要功能特性 -->
    <section class="features-comparison" style="padding: 0 0 64px;">
        <div class="container">
            <h2 class="section-title" data-i18n="features-title">核心功能特性</h2>
            <div class="comparison-grid" style="grid-template-columns: 1fr 1fr; gap: 48px; max-width: 1000px; margin: 0 auto;">
                <div class="feature-comparison">
                    <h3 data-i18n="capabilities-title">主要能力</h3>
                    <ul>
                        <li data-i18n="capability-1">Android原生系统深度集成</li>
                        <li data-i18n="capability-2">高效的3D渲染与资源管理</li>
                        <li data-i18n="capability-3">多媒体与传感器支持</li>
                        <li data-i18n="capability-4">跨平台兼容性保障</li>
                        <li data-i18n="capability-5">便捷的调试与部署工具</li>
                    </ul>
                </div>
                <div class="feature-comparison">
                    <h3 data-i18n="workflow-title">集成流程</h3>
                    <ol>
                        <li data-i18n="workflow-1">集成AndroidFwk模块到3D项目</li>
                        <li data-i18n="workflow-2">配置AndroidManifest及相关权限</li>
                        <li data-i18n="workflow-3">调用API实现3D渲染与系统交互</li>
                        <li data-i18n="workflow-4">打包并部署到Android设备</li>
                    </ol>
                </div>
            </div>

            <!-- 技术优势 -->
            <div class="i18n-grid" style="margin-top: 64px;">
                <div class="i18n-engine">
                    <h3 data-i18n="advantages-title">技术优势</h3>
                    <div class="i18n-features">
                        <div class="i18n-feature">
                            <span class="feature-icon">🚀</span>
                            <div class="feature-content">
                                <h4 data-i18n="advantage-performance">高性能渲染</h4>
                                <p data-i18n="advantage-performance-desc">优化的3D渲染管线，支持硬件加速和GPU优化</p>
                            </div>
                        </div>
                        <div class="i18n-feature">
                            <span class="feature-icon">🔧</span>
                            <div class="feature-content">
                                <h4 data-i18n="advantage-integration">系统集成</h4>
                                <p data-i18n="advantage-integration-desc">深度集成Android系统API和原生功能</p>
                            </div>
                        </div>
                        <div class="i18n-feature">
                            <span class="feature-icon">📱</span>
                            <div class="feature-content">
                                <h4 data-i18n="advantage-compatibility">兼容性</h4>
                                <p data-i18n="advantage-compatibility-desc">支持多种Android版本和设备类型</p>
                            </div>
                        </div>
                        <div class="i18n-feature">
                            <span class="feature-icon">⚡</span>
                            <div class="feature-content">
                                <h4 data-i18n="advantage-efficiency">开发效率</h4>
                                <p data-i18n="advantage-efficiency-desc">简化的API设计，快速集成和部署</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </section>

    <!-- 车载行业应用案例 -->
    <section class="comparison-section" style="padding: 64px 0; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);">
        <div class="container">
            <h2 class="section-title" data-i18n="automotive-cases-title">车载行业应用案例</h2>
            <div class="comparison-intro">
                <p data-i18n="automotive-cases-intro">Android Framework在车载行业的实际应用展示了其在智能座舱、仪表盘和车载娱乐系统中的强大能力</p>
                <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 16px; margin: 24px auto; max-width: 800px;">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px;">
                        <span style="font-size: 20px;">💡</span>
                        <strong style="color: #2d3748;" data-i18n="framework-note-title">关于 Android Framework</strong>
                    </div>
                    <p style="margin: 0; color: #4a5568; line-height: 1.6;" data-i18n="framework-note-desc">
                        Android Framework（简称AndroidFwk）是Android操作系统的应用程序框架层，提供了应用开发所需的核心API和服务。
                        在车载领域，它为HMI系统、传感器集成、多媒体处理等提供了标准化的开发接口和系统级支持。
                    </p>
                </div>
            </div>

            <!-- 案例时间线布局 -->
            <div class="case-timeline" style="margin-top: 64px; position: relative;">
                <!-- 时间线主轴 -->
                <div class="timeline-axis" style="position: absolute; left: 50%; top: 0; bottom: 0; width: 4px; background: linear-gradient(to bottom, #667eea, #764ba2); transform: translateX(-50%); border-radius: 2px;"></div>
                <!-- 案例1：豪华电动车品牌 -->
                <div class="timeline-item" style="display: flex; margin-bottom: 80px; position: relative;">
                    <!-- 时间线节点 -->
                    <div class="timeline-node" style="position: absolute; left: 50%; top: 40px; width: 20px; height: 20px; background: #667eea; border: 4px solid white; border-radius: 50%; transform: translateX(-50%); z-index: 2; box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);"></div>

                    <!-- 案例内容 - 左侧 -->
                    <div class="case-content-left" style="width: 45%; padding-right: 40px; text-align: right;">
                        <div class="case-card" style="background: white; border-radius: 16px; padding: 32px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); border: 1px solid rgba(102, 126, 234, 0.1);">
                            <div class="case-header" style="margin-bottom: 24px;">
                                <div class="case-badge" style="display: inline-block; background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 600; margin-bottom: 16px;">
                                    <span data-i18n="case1-category">智能座舱</span>
                                </div>
                                <h3 style="margin: 0 0 8px 0; color: #2d3748; font-size: 24px; font-weight: 700;" data-i18n="case1-title">豪华电动车品牌智能座舱</h3>
                                <p style="margin: 0; color: #718096; font-size: 16px;" data-i18n="case1-subtitle">多屏融合HMI系统</p>
                            </div>

                            <div class="case-metrics" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 16px; margin-bottom: 24px;">
                                <div class="metric" style="text-align: center; padding: 16px; background: #f7fafc; border-radius: 12px;">
                                    <div style="font-size: 24px; font-weight: 700; color: #667eea;">40%</div>
                                    <div style="font-size: 12px; color: #718096;" data-i18n="metric1-label">启动时间缩短</div>
                                </div>
                                <div class="metric" style="text-align: center; padding: 16px; background: #f7fafc; border-radius: 12px;">
                                    <div style="font-size: 24px; font-weight: 700; color: #48bb78;">30%</div>
                                    <div style="font-size: 12px; color: #718096;" data-i18n="metric2-label">内存占用降低</div>
                                </div>
                                <div class="metric" style="text-align: center; padding: 16px; background: #f7fafc; border-radius: 12px;">
                                    <div style="font-size: 24px; font-weight: 700; color: #ed8936;">95%</div>
                                    <div style="font-size: 12px; color: #718096;" data-i18n="metric3-label">用户满意度</div>
                                </div>
                            </div>

                            <div class="case-details">
                                <h4 style="color: #2d3748; margin-bottom: 12px; font-size: 18px;" data-i18n="case1-challenge">项目挑战</h4>
                                <ul style="margin: 0 0 20px 0; padding-left: 20px; color: #4a5568;">
                                    <li style="margin-bottom: 8px;" data-i18n="case1-challenge-1">15.6英寸中控屏与12.3英寸仪表盘联动</li>
                                    <li style="margin-bottom: 8px;" data-i18n="case1-challenge-2">实时3D导航与车辆状态可视化</li>
                                    <li style="margin-bottom: 8px;" data-i18n="case1-challenge-3">多媒体内容与车载系统深度集成</li>
                                </ul>

                                <h4 style="color: #2d3748; margin-bottom: 12px; font-size: 18px;" data-i18n="case1-solution">解决方案</h4>
                                <p style="color: #4a5568; line-height: 1.6; margin-bottom: 20px;" data-i18n="case1-solution-desc">基于Android Framework构建统一的HMI框架，实现跨屏数据同步和3D渲染优化，支持60fps流畅交互体验。</p>

                                <div class="tech-stack" style="margin-top: 20px;">
                                    <h5 style="color: #2d3748; margin-bottom: 12px; font-size: 16px;" data-i18n="tech-stack-label">技术栈</h5>
                                    <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                                        <span style="background: #e2e8f0; color: #2d3748; padding: 4px 12px; border-radius: 16px; font-size: 12px;">Android Framework</span>
                                        <span style="background: #e2e8f0; color: #2d3748; padding: 4px 12px; border-radius: 16px; font-size: 12px;">OpenGL ES</span>
                                        <span style="background: #e2e8f0; color: #2d3748; padding: 4px 12px; border-radius: 16px; font-size: 12px;">Vulkan</span>
                                        <span style="background: #e2e8f0; color: #2d3748; padding: 4px 12px; border-radius: 16px; font-size: 12px;">CAN Bus</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 案例图片 - 右侧 -->
                    <div class="case-image-right" style="width: 45%; padding-left: 40px; margin-left: 10%;">
                        <div class="image-container" style="position: relative; border-radius: 16px; overflow: hidden; box-shadow: 0 12px 40px rgba(0,0,0,0.15);">
                            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px; text-align: center; color: white;">
                                <div style="font-size: 64px; margin-bottom: 16px;">🚗</div>
                                <h4 style="margin: 0 0 12px 0; font-size: 20px;" data-i18n="case1-image-title">智能座舱系统</h4>
                                <p style="margin: 0; opacity: 0.9; font-size: 14px;" data-i18n="case1-image-desc">多屏联动 • 3D导航 • 实时渲染</p>
                            </div>
                            <div style="background: white; padding: 24px;">
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; text-align: center;">
                                    <div>
                                        <div style="font-size: 20px; font-weight: 700; color: #667eea;">15.6"</div>
                                        <div style="font-size: 12px; color: #718096;" data-i18n="screen1-label">中控屏</div>
                                    </div>
                                    <div>
                                        <div style="font-size: 20px; font-weight: 700; color: #667eea;">12.3"</div>
                                        <div style="font-size: 12px; color: #718096;" data-i18n="screen2-label">仪表盘</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 案例2：商用车队管理 -->
                <div class="timeline-item" style="display: flex; margin-bottom: 80px; position: relative;">
                    <!-- 时间线节点 -->
                    <div class="timeline-node" style="position: absolute; left: 50%; top: 40px; width: 20px; height: 20px; background: #f093fb; border: 4px solid white; border-radius: 50%; transform: translateX(-50%); z-index: 2; box-shadow: 0 4px 12px rgba(240, 147, 251, 0.3);"></div>

                    <!-- 案例图片 - 左侧 -->
                    <div class="case-image-left" style="width: 45%; padding-right: 40px; margin-right: 10%;">
                        <div class="image-container" style="position: relative; border-radius: 16px; overflow: hidden; box-shadow: 0 12px 40px rgba(0,0,0,0.15);">
                            <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); padding: 40px; text-align: center; color: white;">
                                <div style="font-size: 64px; margin-bottom: 16px;">🚛</div>
                                <h4 style="margin: 0 0 12px 0; font-size: 20px;" data-i18n="case2-image-title">车队管理系统</h4>
                                <p style="margin: 0; opacity: 0.9; font-size: 14px;" data-i18n="case2-image-desc">实时监控 • GPS导航 • 数据分析</p>
                            </div>
                            <div style="background: white; padding: 24px;">
                                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 12px; text-align: center;">
                                    <div>
                                        <div style="font-size: 18px; font-weight: 700; color: #f093fb;">25%</div>
                                        <div style="font-size: 11px; color: #718096;" data-i18n="fleet-metric1">效率提升</div>
                                    </div>
                                    <div>
                                        <div style="font-size: 18px; font-weight: 700; color: #f093fb;">15%</div>
                                        <div style="font-size: 11px; color: #718096;" data-i18n="fleet-metric2">燃油节省</div>
                                    </div>
                                    <div>
                                        <div style="font-size: 18px; font-weight: 700; color: #f093fb;">35%</div>
                                        <div style="font-size: 11px; color: #718096;" data-i18n="fleet-metric3">事故降低</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 案例内容 - 右侧 -->
                    <div class="case-content-right" style="width: 45%; padding-left: 40px;">
                        <div class="case-card" style="background: white; border-radius: 16px; padding: 32px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); border: 1px solid rgba(240, 147, 251, 0.1);">
                            <div class="case-header" style="margin-bottom: 24px;">
                                <div class="case-badge" style="display: inline-block; background: linear-gradient(135deg, #f093fb, #f5576c); color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 600; margin-bottom: 16px;">
                                    <span data-i18n="case2-category">车队管理</span>
                                </div>
                                <h3 style="margin: 0 0 8px 0; color: #2d3748; font-size: 24px; font-weight: 700;" data-i18n="case2-title">商用车队管理系统</h3>
                                <p style="margin: 0; color: #718096; font-size: 16px;" data-i18n="case2-subtitle">智能仪表与监控平台</p>
                            </div>

                            <div class="case-features" style="margin-bottom: 24px;">
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                                    <div style="background: #fef5e7; padding: 16px; border-radius: 12px; border-left: 4px solid #f093fb;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 4px;" data-i18n="feature-realtime">实时监控</div>
                                        <div style="font-size: 12px; color: #718096;" data-i18n="feature-realtime-desc">车辆状态实时追踪</div>
                                    </div>
                                    <div style="background: #f0fff4; padding: 16px; border-radius: 12px; border-left: 4px solid #f093fb;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 4px;" data-i18n="feature-navigation">智能导航</div>
                                        <div style="font-size: 12px; color: #718096;" data-i18n="feature-navigation-desc">路线优化算法</div>
                                    </div>
                                </div>
                            </div>

                            <div class="case-details">
                                <h4 style="color: #2d3748; margin-bottom: 12px; font-size: 18px;" data-i18n="case2-challenge">项目挑战</h4>
                                <ul style="margin: 0 0 20px 0; padding-left: 20px; color: #4a5568;">
                                    <li style="margin-bottom: 8px;" data-i18n="case2-challenge-1">实时监控车辆运行状态和驾驶行为</li>
                                    <li style="margin-bottom: 8px;" data-i18n="case2-challenge-2">集成GPS导航与路线优化算法</li>
                                    <li style="margin-bottom: 8px;" data-i18n="case2-challenge-3">支持离线模式和数据同步</li>
                                </ul>

                                <h4 style="color: #2d3748; margin-bottom: 12px; font-size: 18px;" data-i18n="case2-solution">解决方案</h4>
                                <p style="color: #4a5568; line-height: 1.6; margin-bottom: 20px;" data-i18n="case2-solution-desc">利用Android Framework的传感器集成能力，开发智能仪表盘系统，实现车辆数据可视化和远程监控功能。</p>

                                <div class="tech-stack" style="margin-top: 20px;">
                                    <h5 style="color: #2d3748; margin-bottom: 12px; font-size: 16px;" data-i18n="tech-stack-label">技术栈</h5>
                                    <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                                        <span style="background: #e2e8f0; color: #2d3748; padding: 4px 12px; border-radius: 16px; font-size: 12px;">Android Framework</span>
                                        <span style="background: #e2e8f0; color: #2d3748; padding: 4px 12px; border-radius: 16px; font-size: 12px;">GPS SDK</span>
                                        <span style="background: #e2e8f0; color: #2d3748; padding: 4px 12px; border-radius: 16px; font-size: 12px;">OBD-II</span>
                                        <span style="background: #e2e8f0; color: #2d3748; padding: 4px 12px; border-radius: 16px; font-size: 12px;">4G/5G</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 案例3：新能源出行平台 -->
                <div class="timeline-item" style="display: flex; margin-bottom: 80px; position: relative;">
                    <!-- 时间线节点 -->
                    <div class="timeline-node" style="position: absolute; left: 50%; top: 40px; width: 20px; height: 20px; background: #4facfe; border: 4px solid white; border-radius: 50%; transform: translateX(-50%); z-index: 2; box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);"></div>

                    <!-- 案例内容 - 左侧 -->
                    <div class="case-content-left" style="width: 45%; padding-right: 40px; text-align: right;">
                        <div class="case-card" style="background: white; border-radius: 16px; padding: 32px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); border: 1px solid rgba(79, 172, 254, 0.1);">
                            <div class="case-header" style="margin-bottom: 24px;">
                                <div class="case-badge" style="display: inline-block; background: linear-gradient(135deg, #4facfe, #00f2fe); color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 600; margin-bottom: 16px;">
                                    <span data-i18n="case3-category">新能源</span>
                                </div>
                                <h3 style="margin: 0 0 8px 0; color: #2d3748; font-size: 24px; font-weight: 700;" data-i18n="case3-title">新能源出行平台</h3>
                                <p style="margin: 0; color: #718096; font-size: 16px;" data-i18n="case3-subtitle">充电桩互联与能耗管理</p>
                            </div>

                            <div class="case-highlights" style="margin-bottom: 24px;">
                                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px;">
                                    <div style="background: #e6fffa; padding: 16px; border-radius: 12px; text-align: center;">
                                        <div style="font-size: 24px; margin-bottom: 8px;">🔋</div>
                                        <div style="font-weight: 600; color: #2d3748; font-size: 14px;" data-i18n="battery-management">电池管理</div>
                                    </div>
                                    <div style="background: #f0f9ff; padding: 16px; border-radius: 12px; text-align: center;">
                                        <div style="font-size: 24px; margin-bottom: 8px;">🗺️</div>
                                        <div style="font-weight: 600; color: #2d3748; font-size: 14px;" data-i18n="smart-routing">智能路径</div>
                                    </div>
                                </div>
                            </div>

                            <div class="case-details">
                                <h4 style="color: #2d3748; margin-bottom: 12px; font-size: 18px;" data-i18n="case3-challenge">项目挑战</h4>
                                <ul style="margin: 0 0 20px 0; padding-left: 20px; color: #4a5568;">
                                    <li style="margin-bottom: 8px;" data-i18n="case3-challenge-1">实时显示电池状态和充电站信息</li>
                                    <li style="margin-bottom: 8px;" data-i18n="case3-challenge-2">智能路径规划考虑续航里程</li>
                                    <li style="margin-bottom: 8px;" data-i18n="case3-challenge-3">与第三方充电网络API集成</li>
                                </ul>

                                <h4 style="color: #2d3748; margin-bottom: 12px; font-size: 18px;" data-i18n="case3-solution">解决方案</h4>
                                <p style="color: #4a5568; line-height: 1.6; margin-bottom: 20px;" data-i18n="case3-solution-desc">基于Android Framework开发能耗管理HMI，集成3D地图显示和实时数据分析，提供智能充电建议。</p>

                                <div class="tech-stack" style="margin-top: 20px;">
                                    <h5 style="color: #2d3748; margin-bottom: 12px; font-size: 16px;" data-i18n="tech-stack-label">技术栈</h5>
                                    <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                                        <span style="background: #e2e8f0; color: #2d3748; padding: 4px 12px; border-radius: 16px; font-size: 12px;">Android Framework</span>
                                        <span style="background: #e2e8f0; color: #2d3748; padding: 4px 12px; border-radius: 16px; font-size: 12px;">BMS API</span>
                                        <span style="background: #e2e8f0; color: #2d3748; padding: 4px 12px; border-radius: 16px; font-size: 12px;">MapBox</span>
                                        <span style="background: #e2e8f0; color: #2d3748; padding: 4px 12px; border-radius: 16px; font-size: 12px;">REST API</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 案例图片 - 右侧 -->
                    <div class="case-image-right" style="width: 45%; padding-left: 40px; margin-left: 10%;">
                        <div class="image-container" style="position: relative; border-radius: 16px; overflow: hidden; box-shadow: 0 12px 40px rgba(0,0,0,0.15);">
                            <div style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); padding: 40px; text-align: center; color: white;">
                                <div style="font-size: 64px; margin-bottom: 16px;">⚡</div>
                                <h4 style="margin: 0 0 12px 0; font-size: 20px;" data-i18n="case3-image-title">新能源管理</h4>
                                <p style="margin: 0; opacity: 0.9; font-size: 14px;" data-i18n="case3-image-desc">充电优化 • 续航预测 • 智能推荐</p>
                            </div>
                            <div style="background: white; padding: 24px;">
                                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 12px; text-align: center;">
                                    <div>
                                        <div style="font-size: 18px; font-weight: 700; color: #4facfe;">50%</div>
                                        <div style="font-size: 11px; color: #718096;" data-i18n="energy-metric1">充电效率</div>
                                    </div>
                                    <div>
                                        <div style="font-size: 18px; font-weight: 700; color: #4facfe;">60%</div>
                                        <div style="font-size: 11px; color: #718096;" data-i18n="energy-metric2">焦虑降低</div>
                                    </div>
                                    <div>
                                        <div style="font-size: 18px; font-weight: 700; color: #4facfe;">200%</div>
                                        <div style="font-size: 11px; color: #718096;" data-i18n="energy-metric3">用户增长</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 工作流程 -->
            <div class="i18n-content" style="margin-top: 64px;">
                <div class="i18n-text">
                    <h3 data-i18n="workflow-process-title">Android Framework车载开发工作流程</h3>
                    <p data-i18n="workflow-process-desc">基于实际项目经验总结的标准化开发流程，确保项目高效交付和质量保障。</p>
                </div>

                <div class="comparison-grid" style="grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 24px; margin-top: 32px;">
                    <div class="feature-comparison">
                        <div class="i18n-feature">
                            <span class="feature-icon">📋</span>
                            <div class="feature-content">
                                <h4 data-i18n="workflow-step1">需求分析与架构设计</h4>
                                <p data-i18n="workflow-step1-desc">分析车载场景需求，设计HMI架构和数据流，制定技术方案和开发计划。</p>
                            </div>
                        </div>
                    </div>

                    <div class="feature-comparison">
                        <div class="i18n-feature">
                            <span class="feature-icon">⚙️</span>
                            <div class="feature-content">
                                <h4 data-i18n="workflow-step2">环境搭建与集成</h4>
                                <p data-i18n="workflow-step2-desc">配置Android Framework开发环境，集成车载硬件驱动和传感器接口。</p>
                            </div>
                        </div>
                    </div>

                    <div class="feature-comparison">
                        <div class="i18n-feature">
                            <span class="feature-icon">🎨</span>
                            <div class="feature-content">
                                <h4 data-i18n="workflow-step3">UI/UX开发与优化</h4>
                                <p data-i18n="workflow-step3-desc">开发3D界面组件，优化渲染性能，确保车载环境下的可用性。</p>
                            </div>
                        </div>
                    </div>

                    <div class="feature-comparison">
                        <div class="i18n-feature">
                            <span class="feature-icon">🔧</span>
                            <div class="feature-content">
                                <h4 data-i18n="workflow-step4">系统集成与测试</h4>
                                <p data-i18n="workflow-step4-desc">集成车载ECU，进行功能测试、性能测试和安全测试验证。</p>
                            </div>
                        </div>
                    </div>

                    <div class="feature-comparison">
                        <div class="i18n-feature">
                            <span class="feature-icon">🚀</span>
                            <div class="feature-content">
                                <h4 data-i18n="workflow-step5">部署与维护</h4>
                                <p data-i18n="workflow-step5-desc">批量部署到车载设备，提供OTA更新和远程维护支持。</p>
                            </div>
                        </div>
                    </div>

                    <div class="feature-comparison">
                        <div class="i18n-feature">
                            <span class="feature-icon">📊</span>
                            <div class="feature-content">
                                <h4 data-i18n="workflow-step6">数据分析与优化</h4>
                                <p data-i18n="workflow-step6-desc">收集用户使用数据，分析系统性能，持续优化用户体验。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 48px;">
                <a href="../index.html#docs" class="btn btn-primary" data-i18n="btn-back-home">返回技术文档</a>
                <a href="../index.html" class="btn btn-secondary" data-i18n="btn-back-main">返回主页</a>
            </div>
        </div>
    </section>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4 data-i18n="footer-title">3D 引擎技术对比</h4>
                    <p data-i18n="footer-subtitle">专业的 3D 引擎技术分析与选型参考平台</p>
                </div>
                <div class="footer-section">
                    <h4 data-i18n="footer-docs">文档</h4>
                    <ul>
                        <li><a href="qt3d-development-guide.html" data-i18n="footer-link-guide">开发指南</a></li>
                        <li><a href="engine-comparison.html" data-i18n="footer-link-comparison">引擎对比</a></li>
                        <li><a href="internationalization.html" data-i18n="footer-link-i18n">国际化</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 data-i18n="footer-resources">资源</h4>
                    <ul>
                        <li><a href="https://doc.qt.io/qt-6/qt3d-index.html" target="_blank" data-i18n="footer-link-official-docs">官方文档</a></li>
                        <li><a href="https://github.com/qt" target="_blank" data-i18n="footer-link-github">GitHub</a></li>
                        <li><a href="https://forum.qt.io/" target="_blank" data-i18n="footer-link-forum">社区论坛</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p data-i18n="footer-copyright">&copy; 2024 3D 引擎技术对比平台. 为开发者提供专业技术参考.</p>
            </div>
        </div>
    </footer>

    <script src="../assets/js/prism.js"></script>
    <script src="../assets/js/main.js"></script>
</body>
</html> 